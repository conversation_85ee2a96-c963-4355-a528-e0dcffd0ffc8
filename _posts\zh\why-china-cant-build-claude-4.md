---
title: "为什么国内做不出 Claude 4？我们到底差在哪？"
excerpt: "Claude 4 的强大能力再次让人感叹中美AI技术差距。作为一个技术从业者，我想聊聊为什么国内总是做不出这种现象级的AI模型，我们到底差在哪里？"
coverImage: "/assets/blog/claude-4-analysis.png"
featured: true
featuredOrder: 2
date: "2025-01-15"
lastModified: "2025-01-15"
author:
  name: 老夫撸代码
  picture: "/assets/blog/authors/tim.jpeg"
---

最近又在用 Claude 4，每次用完都有种复杂的感觉。

说实话，每次体验这些顶级 AI 模型，我都有种"又被人家甩开一大截"的感觉。不是酸葡萄心理，而是真的觉得差距越来越大了。

前几天和朋友吃饭，聊到这个话题，他问我："你说咱们国内这么多聪明人，这么多钱，为啥就是搞不出 GPT、Claude 这种东西？"

我想了想，这问题确实值得好好聊聊。看着手里 0.01 元撸的棒打鲜橙，我陷入了沉思。

说白了，咱们的资本更喜欢赚快钱，赚老百姓的钱。盯着菜篮子、钱袋子，搞个外卖平台、团购 APP，立马就能看到现金流。

有时候真觉得像地主家的傻儿子——让别人先把技术搞出来，咱们再用钱砸，搞收购，包装上市，卖给散户。赚一波就跑，然后拿着钱去海外逍遥快活。

这种思路搞应用、搞商业模式创新确实很厉害，但搞基础技术？算了吧。

## 乍一看，咱们啥都不缺啊

### 人才？多得是

你说人才，国内真不缺。我在腾讯的时候，隔壁组就有好几个从 Google、Facebook 回来的大牛。清华姚班出来的那些人，个个都是天才级别的。

前段时间参加一个 AI 会议，台上讲的那些教授，论文引用量都是几万几万的。技术水平绝对不比硅谷那边差。

我一个朋友，CMU 机器学习博士毕业，现在在字节做大模型。聊天的时候他说，OpenAI 那些人的技术水平，也就那样，没有想象中那么神。

### 钱？更不是问题

要说烧钱，咱们可是专业的。

阿里云去年光是买 GPU 就花了几百亿，腾讯云也不甘示弱。字节跳动为了训练模型，直接包了好几个数据中心。

我听说百度为了做文心一言，前前后后投了上千亿。这个数字，比 OpenAI 从成立到现在的总投入还要多。

### 算力？凑合能用

虽然被老美卡脖子，高端芯片买不到，但咱们也不是完全没办法。

华为的昇腾 910，虽然比不上 A100，但训练个小模型还是可以的。而且数量多啊，堆起来也能用。

我去过阿里云的数据中心，那规模真的震撼。一排排的服务器，密密麻麻的，看着就觉得算力充足。

## 那问题到底出在哪？

### 我们总是在跟跑，很少领跑

这个问题我想了很久。

你看啊，Transformer 是 Google 搞出来的，GPT 是 OpenAI 的，Claude 背后的 Constitutional AI 是 Anthropic 发明的。这些改变游戏规则的技术，基本都是人家先搞出来的。

咱们呢？更多时候是在人家的基础上做优化。比如说，ChatGPT 火了之后，国内一堆公司都在做"中文版 ChatGPT"。但说白了，就是拿 Transformer 架构，换点中文数据，调调参数。

我在某大厂待过一段时间，亲眼见过这种项目。老板拍桌子说要做"中国的 GPT"，结果团队忙活了半年，做出来的东西本质上还是在人家的框架里打转。

真正从 0 到 1 的原创性突破，确实不多见。

### 急功近利，缺乏长期主义

这个问题可能更要命。

你看 OpenAI，Sam Altman 他们从 2015 年开始就在赌 AGI 这个方向。中间好几次差点倒闭，投资人都不看好，但他们硬是坚持下来了。

Anthropic 更夸张，Dario Amodei 从 OpenAI 出来，专门研究 AI 安全。这玩意儿当时看起来完全没有商业价值，就是纯粹的理想主义。但人家就是坚持做了好几年，最后做出了 Claude。

咱们这边呢？我见过太多这样的故事：

去年我朋友的公司立项做大模型，老板信心满满，说要做"中国的 ChatGPT"。结果做了三个月，发现训练成本太高，改成做垂直领域的小模型。又做了三个月，发现效果不好，最后变成了一个客服机器人。

现在这个项目还在，但已经和当初的目标完全不一样了。

投资人也是这样，动不动就问："什么时候能盈利？商业模式是什么？"很少有人愿意投那种看起来很"虚"的基础研究。

### 数据问题，真的很头疼

这个问题我深有体会。

OpenAI 当年训练 GPT 的时候，直接把整个互联网都爬了一遍。Reddit、Twitter、Wikipedia、各种论坛、博客，只要是公开的文本，基本都拿来训练了。虽然后来有版权争议，但确实获得了海量的高质量数据。

咱们这边就麻烦了。

我有个朋友在某大厂做大模型，他跟我吐槽过这个问题。想用某个数据集，法务部门说有合规风险；想爬某个网站的数据，合规部门说不行，可能涉及隐私问题。

最后只能用一些公开的、已经清洗过的数据集。但这些数据集的质量和数量，跟人家直接爬整个互联网比起来，差距太大了。

而且中文互联网的高质量内容本来就不多，很多好的内容都在微信公众号、知乎、抖音这些相对封闭的平台里。想要获取这些数据，难度更大。

### 人才都在大厂内卷，很少出来创业

还有一个问题，就是人才流动。

硅谷那边，OpenAI 的核心团队很多都是从 Google Brain、DeepMind 跳过来的。Anthropic 的创始团队更直接，就是从 OpenAI 出来的。大家在不同公司之间跳来跳去，技术和经验也跟着流动。

咱们这边呢？顶尖人才基本都在大厂里，而且很少出来创业。

我认识几个技术很牛的朋友，都在腾讯、阿里这些大厂。问他们为什么不出来做点什么，他们说："大厂待着挺好的，工资高，福利好，为什么要出来受罪？"

而且国内的技术圈相对封闭。很多核心技术都在公司内部，不像硅谷那样有很多开源项目。大家各自为政，缺乏交流。

### 投资人只看短期回报

最后一个问题，就是投资理念。

我参加过不少 AI 项目的路演，投资人问的最多的问题就是："什么时候能盈利？商业模式是什么？用户付费意愿怎么样？"

很少有人问："这个技术有什么突破性？能不能改变行业？"

硅谷那边不一样，他们愿意投那些看起来很"疯狂"的项目。OpenAI 早期的时候，很多人觉得 AGI 就是天方夜谭，但还是有投资人愿意赌一把。

这种差异，导致我们很难支持那些需要长期投入、短期看不到回报的基础研究。

## 但咱们也有自己的优势

说了这么多问题，但也不能妄自菲薄。国内在某些方面确实做得不错。

### 应用落地能力确实强

虽然基础模型做不过人家，但咱们把 AI 技术落地的能力是真的强。

抖音的推荐算法，美团的配送路径优化，滴滴的智能调度，这些都是世界级的 AI 应用。用户体验做得比很多硅谷公司都好。

我们对用户需求的理解很深，知道中国用户喜欢什么，能够快速把 AI 技术包装成用户喜欢的产品。

### 工程能力没得说

国内程序员的工程能力，全世界都认。给我们一个技术方案，我们能够快速实现，而且质量很高。

ChatGPT 火了之后，国内陆续推出了文心一言、通义千问、讯飞星火这些产品。虽然模型能力有差距，但产品体验做得都挺不错的。

### 场景多，数据丰富

中国有 14 亿人口，各种奇葩的应用场景都有。这为 AI 技术的落地提供了很好的试验田。

而且中文的复杂性，也给 AI 研究带来了独特的挑战。搞定了中文，其他语言相对就简单多了。

## 那我们应该怎么办？

### 真正重视基础研究

这个话题说了很多年了，但我觉得还是要继续说。

不只是投钱，更重要的是给时间、给耐心。学学 Anthropic 的做法，选定一个方向，长期投入，不要总想着快速变现。

### 改变投资思维

投资人和创业者都得改改思维。有些技术就是需要长期投入的，不能总是想着快速回报。

政府的引导基金可以在这方面发挥作用，专门支持那些有长远价值但短期看不到钱的项目。

### 鼓励人才流动

现在顶尖人才都在大厂里待着，缺乏流动。应该鼓励更多的人出来创业，或者在不同机构之间流动。

而且要营造更开放的技术氛围，多搞一些技术分享，多做一些开源项目。

### 数据问题得想办法解决

在保护隐私的前提下，探索更灵活的数据使用方式。联邦学习、差分隐私这些技术，可能是个方向。

### 该合作还是要合作

虽然现在国际环境比较复杂，但在基础研究层面，该合作还是要合作。科学技术本来就没有国界。

## 最后想说的

写这篇文章，不是为了长他人志气，灭自己威风。而是觉得有些问题确实需要正视。

Claude 4 确实很厉害，但这不代表我们永远追不上。关键是要找对方向，别总是在错误的路上狂奔。

我还是相信，以中国人的聪明才智，只要方法对了，做出世界级的 AI 模型只是时间问题。

但这需要我们改变一些固有的思维：

- 别总想着快速变现，有些事情就是需要时间
- 多投入基础研究，别总是跟在人家后面
- 开放一点，别总是各自为政

**差距是客观存在的，但差距不是永恒的。关键是要承认差距，然后想办法缩小差距。**

我觉得，总有一天我们也能做出让全世界震撼的 AI 模型。到那时候，我们就不用再问"为什么做不出 Claude 4"了，而是要问"下一个突破在哪里"。

你觉得呢？我们真的能追上吗？
