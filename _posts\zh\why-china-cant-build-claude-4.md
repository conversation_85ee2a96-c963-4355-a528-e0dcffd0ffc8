---
title: "为什么国内做不出 Claude 4？我们到底差在哪？"
excerpt: "Claude 4 的发布再次让人感叹中美AI技术差距。作为一个技术从业者，我想聊聊为什么国内总是做不出这种现象级的AI模型，我们到底差在哪里？"
coverImage: "/assets/blog/claude-4-analysis.png"
featured: true
featuredOrder: 2
date: "2025-01-15"
lastModified: "2025-01-15"
author:
  name: 老夫撸代码
  picture: "/assets/blog/authors/tim.jpeg"
---

Claude 4 发布了，又是一次技术震撼。看着 Anthropic 展示的各种能力，说不羡慕是假的。

作为一个在国内科技圈摸爬滚打了十几年的技术人，我经常被问一个问题：为什么国内总是做不出 GPT、Claude 这种现象级的 AI 模型？

今天想聊聊这个话题，不是为了贬低谁，而是想理性分析一下差距到底在哪里。

## 表面上看，我们好像不差什么

### 人才？我们有

国内 AI 人才其实不少。清华、北大、中科院的 AI 实验室水平都很高，很多顶尖学者都是华人。百度、阿里、腾讯、字节的 AI 团队也都有世界级的专家。

我认识的几个朋友，都是从硅谷回来的 AI 博士，技术能力绝对不输给 OpenAI 或 Anthropic 的工程师。

### 资金？我们也有

钱更不是问题。阿里云、腾讯云每年在 AI 上的投入都是百亿级别。字节跳动光是买 GPU 就花了几十亿。

相比之下，Anthropic 到现在总融资也就几十亿美元，OpenAI 也是最近才拿到大笔投资。

### 算力？我们还行

虽然受到芯片制裁，但国内的算力总量还是很可观的。华为的昇腾、寒武纪的 MLU，虽然和 A100、H100 有差距，但也不是完全不能用。

而且我们有全世界最大的数据中心建设能力，基础设施这块真不差。

## 那问题出在哪里？

### 1. 基础研究的差距

虽然我们有很多 AI 人才，但在基础研究上确实有差距。

**Transformer 架构**是 Google 提出的，**GPT 系列**是 OpenAI 做的，**Constitutional AI**（Claude 的核心技术）是 Anthropic 发明的。这些奠定现代大模型基础的技术，几乎都来自美国。

我们更多是在应用层面创新，比如怎么把大模型用到搜索、推荐、客服上。但在底层的模型架构、训练方法上，原创性的突破确实不多。

举个例子，国内很多公司都在做"中文版 ChatGPT"，但基本都是在 Transformer 架构基础上调参数、换数据。真正从零开始设计新架构的，几乎没有。

### 2. 工程文化的差异

这个可能更关键。硅谷的工程文化和我们确实不一样。

**OpenAI 的故事**：Sam Altman 他们从 2015 年开始就在赌 AGI，连续烧钱好几年，中间无数次差点倒闭。但他们坚持下来了，最终做出了 GPT。

**Anthropic 的故事**：Dario Amodei 从 OpenAI 出来创业，专门研究 AI 安全。这个方向当时看起来很"虚"，但他们坚持做了好几年，最终做出了 Claude。

反观国内，我们的公司更现实一些。投资人要看 ROI，老板要看营收，很少有人愿意在一个不确定的方向上烧钱好几年。

我见过太多这样的项目：
- 立项的时候雄心壮志，要做"中国的 GPT"
- 做了半年发现太难了，改成做垂直领域应用
- 再做半年发现应用也不好做，最后变成了一个聊天机器人

### 3. 数据和隐私的矛盾

大模型需要海量数据训练，但国内对数据使用的限制越来越严。

**国外的做法**：OpenAI 直接爬了整个互联网，包括 Reddit、Twitter、Wikipedia 等等。虽然有争议，但确实获得了海量的高质量数据。

**国内的现状**：数据合规要求越来越严，很多公司不敢随便使用网络数据。而且中文互联网的高质量内容相对较少，很多都在微信、抖音这些封闭平台里。

我有个朋友在某大厂做大模型，他说最头疼的就是数据问题。想用某个数据集，法务说有风险；想爬某个网站，合规说不行。最后只能用一些公开数据集，质量和数量都不够。

### 4. 人才流动的问题

这个可能是最核心的问题。

**硅谷的人才流动**：OpenAI 的核心团队很多来自 Google Brain、DeepMind。Anthropic 的创始团队直接从 OpenAI 出来。大家在不同公司之间流动，技术和经验也在流动。

**国内的现状**：人才流动相对较少，而且主要在大厂之间。真正愿意去创业公司做基础研究的顶尖人才不多。

而且国内的技术圈相对封闭，很多核心技术都在公司内部，不像硅谷那样有很多开源项目和技术分享。

### 5. 投资理念的差异

这个差异可能被低估了。

**硅谷的投资逻辑**：愿意投资那些看起来很"疯狂"的项目。OpenAI 早期的时候，很多人觉得 AGI 是天方夜谭，但还是有投资人愿意赌。

**国内的投资逻辑**：更看重短期回报和商业模式。投资人经常问的是："这个技术什么时候能赚钱？商业模式是什么？"

我参加过很多 AI 项目的路演，投资人最关心的往往不是技术有多先进，而是能不能快速变现。这种思维下，很难支持那些需要长期投入的基础研究。

## 我们在哪些方面做得不错？

说了这么多问题，但也要承认，国内在某些方面确实做得不错。

### 应用创新能力强

虽然基础模型不如人家，但我们在应用层面的创新能力很强。

**抖音的推荐算法**、**美团的配送优化**、**滴滴的路径规划**，这些都是世界级的 AI 应用。

而且我们对用户需求的理解很深，能够快速把 AI 技术转化为用户喜欢的产品。

### 工程实现能力强

国内程序员的工程能力是世界公认的。给我们一个技术方案，我们能够快速实现，而且质量很高。

比如 ChatGPT 火了之后，国内很快就出现了各种类似的产品。虽然模型能力有差距，但产品体验做得都不错。

### 场景丰富，数据多样

中国有 14 亿人口，各种应用场景都有。这为 AI 技术的落地提供了很好的土壤。

而且中文语言的复杂性，也为 AI 研究提供了独特的挑战和机会。

## 我们应该怎么办？

### 1. 重视基础研究

这个说了很多年，但确实需要更多投入。不只是钱，更重要的是时间和耐心。

我觉得可以学习一下 Anthropic 的做法：专注一个方向，长期投入，不急于变现。

### 2. 改变投资理念

投资人和创业者都需要改变思维。有些技术就是需要长期投入，不能总想着快速回报。

政府的引导基金可以在这方面发挥作用，支持那些有长远价值但短期看不到回报的项目。

### 3. 加强人才流动

鼓励更多的人才在不同机构之间流动，包括高校、企业、研究院所。

而且要营造更开放的技术氛围，鼓励技术分享和开源贡献。

### 4. 数据治理创新

在保护隐私的前提下，探索更灵活的数据使用方式。比如联邦学习、差分隐私等技术。

### 5. 国际合作

虽然有一些限制，但在基础研究层面，还是应该加强国际合作。科学无国界，技术交流对双方都有好处。

## 写在最后

说了这么多，不是为了长他人志气，灭自己威风。而是希望我们能够正视差距，找到问题的根源。

Claude 4 确实很厉害，但这不意味着我们永远追不上。关键是要找对方向，持续投入。

我相信，以中国人的聪明才智和勤奋精神，只要方法对了，做出世界级的 AI 模型只是时间问题。

但这需要我们改变一些固有的思维模式：
- 从追求短期回报到长期投入
- 从应用创新到基础研究
- 从封闭开发到开放合作

**技术没有国界，但技术竞争确实存在。我们要做的，不是抱怨或者焦虑，而是踏踏实实地补短板，发挥长处。**

相信不久的将来，我们也能看到"Made in China"的现象级 AI 模型。

到那时候，我们就不用再问"为什么做不出 Claude 4"了，而是要问"下一个突破在哪里"。

你觉得呢？
